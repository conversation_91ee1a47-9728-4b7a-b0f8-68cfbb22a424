import type { DataTableTokenSections } from '@primeuix/themes/types/datatable';

export * from '@primeuix/themes/types/datatable';

declare const root: DataTableTokenSections.Root;
declare const header: DataTableTokenSections.Header;
declare const headerCell: DataTableTokenSections.HeaderCell;
declare const columnTitle: DataTableTokenSections.ColumnTitle;
declare const row: DataTableTokenSections.Row;
declare const bodyCell: DataTableTokenSections.BodyCell;
declare const footerCell: DataTableTokenSections.FooterCell;
declare const columnFooter: DataTableTokenSections.ColumnFooter;
declare const footer: DataTableTokenSections.Footer;
declare const dropPoint: DataTableTokenSections.DropPoint;
declare const columnResizer: DataTableTokenSections.ColumnResizer;
declare const resizeIndicator: DataTableTokenSections.ResizeIndicator;
declare const sortIcon: DataTableTokenSections.SortIcon;
declare const loadingIcon: DataTableTokenSections.LoadingIcon;
declare const rowToggleButton: DataTableTokenSections.RowToggleButton;
declare const filter: DataTableTokenSections.Filter;
declare const paginatorTop: DataTableTokenSections.PaginatorTop;
declare const paginatorBottom: DataTableTokenSections.PaginatorBottom;
declare const colorScheme: DataTableTokenSections.ColorScheme;
declare const _default: {
    root: DataTableTokenSections.Root;
    header: DataTableTokenSections.Header;
    headerCell: DataTableTokenSections.HeaderCell;
    columnTitle: DataTableTokenSections.ColumnTitle;
    row: DataTableTokenSections.Row;
    bodyCell: DataTableTokenSections.BodyCell;
    footerCell: DataTableTokenSections.FooterCell;
    columnFooter: DataTableTokenSections.ColumnFooter;
    footer: DataTableTokenSections.Footer;
    dropPoint: DataTableTokenSections.DropPoint;
    columnResizer: DataTableTokenSections.ColumnResizer;
    resizeIndicator: DataTableTokenSections.ResizeIndicator;
    sortIcon: DataTableTokenSections.SortIcon;
    loadingIcon: DataTableTokenSections.LoadingIcon;
    rowToggleButton: DataTableTokenSections.RowToggleButton;
    filter: DataTableTokenSections.Filter;
    paginatorTop: DataTableTokenSections.PaginatorTop;
    paginatorBottom: DataTableTokenSections.PaginatorBottom;
    colorScheme: DataTableTokenSections.ColorScheme;
};

export {
    bodyCell,
    colorScheme,
    columnFooter,
    columnResizer,
    columnTitle,
    _default as default,
    dropPoint,
    filter,
    footer,
    footerCell,
    header,
    headerCell,
    loadingIcon,
    paginatorBottom,
    paginatorTop,
    resizeIndicator,
    root,
    row,
    rowToggleButton,
    sortIcon
};
