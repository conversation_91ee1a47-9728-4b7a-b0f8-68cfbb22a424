{"name": "playground", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"vite": "^6.1.0", "@primeuix/forms": "workspace:*", "@primeuix/locale": "workspace:*", "@primeuix/styled": "workspace:*", "@primeuix/styles": "workspace:*", "@primeuix/themes": "workspace:*", "@primeuix/utils": "workspace:*"}}