name: Stale Issues and PRs
on:
  schedule:
    - cron: '0 0 * * *'
permissions:
  contents: read

jobs:
  stale:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-label: 'Resolution: Stale'
          stale-pr-label: 'Resolution: Stale'
          exempt-issue-labels: 'Core Team,PRO Support,good first issue,question,Resolution: Help Wanted,Resolution: Workaround,Status: Discussion,Status: In Progress,Status: Needs Triage,Status: Pending Review,Type: Enhancement,Type: New Feature'
          exempt-pr-labels: 'Core Team,Resolution: Help Wanted,Resolution: Workaround,Status: Discussion,Status: In Progress,Status: Pending Review'
          exempt-all-milestones: true
          days-before-stale: 360
          days-before-close: 7
          close-issue-reason: 'not_planned'
          stale-issue-message: 'This issue has been automatically marked as stale. **If this issue is still affecting you with the latest version, please leave any comment**, and we will keep it open. We are sorry that we have not been able to prioritize it yet. If you have any new additional information, please include it with your comment!'
          close-issue-message: 'Closing this issue after a prolonged period of inactivity. If this issue is still present in the latest release, please create a new issue with up-to-date information. Thank you for your understanding!'
          stale-pr-message: 'This pull request has been automatically marked as stale. **If this pull request is still relevant, please leave any comment**, and we will keep it open. We are sorry that we have not been able to prioritize reviewing it yet. Your contribution is very much appreciated.'
          close-pr-message: 'Closing this pull request after a prolonged period of inactivity. If this issue is still present in the latest release, please ask for this pull request to be reopened. Thank you for your understanding!'
