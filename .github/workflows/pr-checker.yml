name: Pr Checker

on:
  pull_request:
    types: [edited, synchronize, opened, reopened]

permissions:
  contents: read
  pull-requests: write

jobs:
  pr-open-check:
    permissions:
      issues: write
      pull-requests: write
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Verify Linked Issue
        uses: hattan/verify-linked-issue-action@v1.1.5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          message: 'Thanks a lot for your contribution! But, PR does not seem to be linked to any issues. Please [manually link to an issue](https://docs.github.com/en/issues/tracking-your-work-with-issues/linking-a-pull-request-to-an-issue#manually-linking-a-pull-request-or-branch-to-an-issue-using-the-issue-sidebar).'

      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'pnpm'

      - name: Install dependencies
        run: |
          pnpm install --no-frozen-lockfile

      - name: Test
        run: |
          pnpm run test

      - name: Format
        id: codeFormat
        run: |
          pnpm run format:check

      - name: Add Code Format Fail Comment
        if: always() && steps.codeFormat.outcome == 'failure'
        uses: actions-cool/issues-helper@v3
        with:
          actions: 'create-comment'
          token: ${{ secrets.GITHUB_TOKEN }}
          issue-number: ${{ github.event.issue.number }}
          body: |
            Thanks a lot for your contribution! But, PR does not seem to fit our code format standards. Please run the 'pnpm run format' command and commit the changes.

      - name: Build
        run: |
          pnpm run build
