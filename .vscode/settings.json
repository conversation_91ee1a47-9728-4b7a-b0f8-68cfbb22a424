{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "javascript.referencesCodeLens.enabled": true, "eslint.useFlatConfig": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[css]": {"editor.defaultFormatter": "si<PERSON><PERSON>-s.vscode-scss-formatter", "editor.formatOnSave": true}, "[scss]": {"editor.defaultFormatter": "si<PERSON><PERSON>-s.vscode-scss-formatter", "editor.formatOnSave": true}, "vue.server.hybridMode": "typeScriptPluginOnly", "search.exclude": {"**/dist": true, "**/node_modules": true, "submodules/**": true}}